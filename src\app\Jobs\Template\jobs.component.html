<!-- <PERSON> Header -->
<section class="page-header bg-primary text-white py-5">
  <div class="container">
    <div class="row">
      <div class="col-12 text-center">
        <h1 class="display-4 fw-bold mb-3">Find Your Perfect Job</h1>
        <p class="lead">Discover amazing career opportunities from top companies</p>
      </div>
    </div>
  </div>
</section>

<!-- Search and Filter Section -->
<section class="search-filter-section py-4 bg-light">
  <div class="container">
    <div class="card shadow-sm">
      <div class="card-body p-4">
        <form (ngSubmit)="searchJobs()" class="row g-3">
          <!-- Search Keyword -->
          <div class="col-lg-3 col-md-6">
            <label for="searchKeyword" class="form-label">Job Title or Keywords</label>
            <input
              type="text"
              id="searchKeyword"
              class="form-control"
              placeholder="e.g. Software Developer"
              [(ngModel)]="searchKeyword"
              name="searchKeyword">
          </div>

          <!-- Location -->
          <div class="col-lg-3 col-md-6">
            <label for="location" class="form-label">Location</label>
            <input
              type="text"
              id="location"
              class="form-control"
              placeholder="e.g. New York, NY"
              [(ngModel)]="selectedLocation"
              name="selectedLocation">
          </div>

          <!-- Category -->
          <div class="col-lg-2 col-md-6">
            <label for="category" class="form-label">Category</label>
            <select
              id="category"
              class="form-select"
              [(ngModel)]="selectedCategory"
              name="selectedCategory">
              <option value="">All Categories</option>
              <option *ngFor="let category of categories" [value]="category">{{category}}</option>
            </select>
          </div>

          <!-- Job Type -->
          <div class="col-lg-2 col-md-6">
            <label for="jobType" class="form-label">Job Type</label>
            <select
              id="jobType"
              class="form-select"
              [(ngModel)]="selectedJobType"
              name="selectedJobType">
              <option value="">All Types</option>
              <option *ngFor="let type of jobTypes" [value]="type">{{type}}</option>
            </select>
          </div>

          <!-- Action Buttons -->
          <div class="col-lg-2 col-md-12">
            <label class="form-label d-block">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary flex-fill" [disabled]="loading">
                <i class="fas fa-search me-1"></i>
                <span *ngIf="!loading">Search</span>
                <span *ngIf="loading">
                  <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                  Searching...
                </span>
              </button>
              <button type="button" class="btn btn-outline-secondary" (click)="clearFilters()">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<!-- Results Section -->
<section class="results-section py-5">
  <div class="container">
    <!-- Results Header -->
    <div class="row mb-4">
      <div class="col-md-6">
        <h3 class="mb-0">
          <span *ngIf="!loading">{{filteredJobs.length}} Jobs Found</span>
          <span *ngIf="loading">Searching Jobs...</span>
        </h3>
        <p class="text-muted mb-0" *ngIf="!loading">
          Showing {{((currentPage - 1) * itemsPerPage) + 1}} -
          {{Math.min(currentPage * itemsPerPage, filteredJobs.length)}} of {{filteredJobs.length}} results
        </p>
      </div>
      <div class="col-md-6 text-md-end">
        <div class="dropdown">
          <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            Sort By: Newest First
          </button>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#">Newest First</a></li>
            <li><a class="dropdown-item" href="#">Oldest First</a></li>
            <li><a class="dropdown-item" href="#">Salary: High to Low</a></li>
            <li><a class="dropdown-item" href="#">Salary: Low to High</a></li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <div *ngIf="error" class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{error}}
      <button type="button" class="btn btn-sm btn-outline-danger ms-3" (click)="loadAllJobs()">
        Try Again
      </button>
    </div>

    <!-- Loading Spinner -->
    <div *ngIf="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3 text-muted">Loading jobs...</p>
    </div>

    <!-- No Results -->
    <div *ngIf="!loading && filteredJobs.length === 0" class="text-center py-5">
      <i class="fas fa-search fa-3x text-muted mb-3"></i>
      <h4>No Jobs Found</h4>
      <p class="text-muted">Try adjusting your search criteria or browse all available positions.</p>
      <button class="btn btn-primary" (click)="clearFilters()">View All Jobs</button>
    </div>

    <!-- Job Listings -->
    <div *ngIf="!loading && filteredJobs.length > 0" class="row">
      <div class="col-12">
        <!-- Job Cards -->
        <div *ngFor="let job of paginatedJobs" class="job-card card border-0 shadow-sm mb-4">
          <div class="card-body p-4">
            <div class="row">
              <!-- Company Logo and Basic Info -->
              <div class="col-lg-8">
                <div class="d-flex align-items-start mb-3">
                  <div class="company-logo bg-primary rounded me-3 d-flex align-items-center justify-content-center"
                       style="width: 60px; height: 60px; min-width: 60px;">
                    <i class="fas fa-building text-white fa-lg"></i>
                  </div>
                  <div class="flex-grow-1">
                    <h5 class="job-title mb-1">{{job.title}}</h5>
                    <p class="company-name text-primary mb-2">{{job.company}}</p>
                    <div class="job-meta d-flex flex-wrap gap-2 mb-2">
                      <span class="badge bg-outline-secondary">
                        <i class="fas fa-map-marker-alt me-1"></i>{{job.location}}
                      </span>
                      <span class="badge bg-primary">{{job.jobType}}</span>
                      <span *ngIf="job.isRemote" class="badge bg-success">Remote</span>
                      <span *ngIf="job.salary" class="badge bg-info">{{job.salary}}</span>
                    </div>
                  </div>
                </div>

                <!-- Job Description -->
                <p class="job-description text-muted mb-3">
                  {{job.description}}
                </p>

                <!-- Requirements -->
                <div *ngIf="job.requirements && job.requirements.length > 0" class="requirements mb-3">
                  <small class="text-muted d-block mb-2">Requirements:</small>
                  <div class="d-flex flex-wrap gap-1">
                    <span *ngFor="let req of job.requirements" class="badge bg-light text-dark">{{req}}</span>
                  </div>
                </div>
              </div>

              <!-- Action Column -->
              <div class="col-lg-4 text-lg-end">
                <div class="d-flex flex-column h-100 justify-content-between">
                  <div>
                    <small class="text-muted">Posted {{formatDate(job.postedDate)}}</small>
                  </div>
                  <div class="mt-3">
                    <button class="btn btn-primary btn-lg w-100 mb-2">
                      <i class="fas fa-paper-plane me-2"></i>Apply Now
                    </button>
                    <button class="btn btn-outline-secondary w-100">
                      <i class="fas fa-heart me-2"></i>Save Job
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <nav *ngIf="totalPages > 1" aria-label="Job listings pagination" class="mt-5">
          <ul class="pagination justify-content-center">
            <!-- Previous Button -->
            <li class="page-item" [class.disabled]="currentPage === 1">
              <button class="page-link" (click)="changePage(currentPage - 1)" [disabled]="currentPage === 1">
                <i class="fas fa-chevron-left"></i>
                Previous
              </button>
            </li>

            <!-- First Page -->
            <li *ngIf="currentPage > 3" class="page-item">
              <button class="page-link" (click)="changePage(1)">1</button>
            </li>
            <li *ngIf="currentPage > 4" class="page-item disabled">
              <span class="page-link">...</span>
            </li>

            <!-- Page Numbers -->
            <li *ngFor="let page of getPageNumbers()"
                class="page-item"
                [class.active]="page === currentPage">
              <button class="page-link" (click)="changePage(page)">{{page}}</button>
            </li>

            <!-- Last Page -->
            <li *ngIf="currentPage < totalPages - 3" class="page-item disabled">
              <span class="page-link">...</span>
            </li>
            <li *ngIf="currentPage < totalPages - 2" class="page-item">
              <button class="page-link" (click)="changePage(totalPages)">{{totalPages}}</button>
            </li>

            <!-- Next Button -->
            <li class="page-item" [class.disabled]="currentPage === totalPages">
              <button class="page-link" (click)="changePage(currentPage + 1)" [disabled]="currentPage === totalPages">
                Next
                <i class="fas fa-chevron-right"></i>
              </button>
            </li>
          </ul>
        </nav>

        <!-- Results Summary -->
        <div class="text-center mt-4">
          <p class="text-muted">
            Showing {{((currentPage - 1) * itemsPerPage) + 1}} -
            {{Math.min(currentPage * itemsPerPage, filteredJobs.length)}} of {{filteredJobs.length}} jobs
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action Section -->
<section class="cta-section py-5 bg-primary text-white">
  <div class="container">
    <div class="row text-center">
      <div class="col-12">
        <h3 class="mb-3">Can't Find the Right Job?</h3>
        <p class="lead mb-4">Create a job alert and get notified when new opportunities match your criteria</p>
        <div class="d-flex justify-content-center gap-3 flex-wrap">
          <button class="btn btn-light btn-lg px-4">
            <i class="fas fa-bell me-2"></i>Create Job Alert
          </button>
          <a routerLink="/Contact" class="btn btn-outline-light btn-lg px-4">
            <i class="fas fa-envelope me-2"></i>Contact Us
          </a>
        </div>
      </div>
    </div>
  </div>
</section>
