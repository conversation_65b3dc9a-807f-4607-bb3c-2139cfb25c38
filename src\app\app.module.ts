import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HeaderComponent } from './HeaderFooter/Component/header.component';
import { FooterComponent } from './HeaderFooter/Component/footer.component';
import { RouterModule } from '@angular/router';
import { HomeModule } from '../app/Home/home.module';
import { JobsComponent } from './Jobs/Component/jobs.component';
import { JobsModule } from './Jobs/jobs.module';

@NgModule({
  declarations: [
    AppComponent,
    HeaderComponent,
    FooterComponent,
  ],
  imports: [
    HomeModule,
    JobsModule,
    BrowserModule,
    AppRoutingModule,
    RouterModule
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
