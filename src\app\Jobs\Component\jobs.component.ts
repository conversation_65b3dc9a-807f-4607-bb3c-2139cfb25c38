import { Component, OnInit } from '@angular/core';
import { CoreDataService } from '../../Common/core-data.service';

@Component({
  selector: 'app-jobs',
  templateUrl: '../Template/jobs.component.html',
  styleUrl: '../Style/jobs.component.css'
})
export class JobsComponent implements OnInit {
  jobs: any[] = [];
  filteredJobs: any[] = [];
  loading: boolean = false;
  error: string = '';

  // Make Math available in template
  Math = Math;

  // Filter properties
  searchKeyword: string = '';
  selectedLocation: string = '';
  selectedCategory: string = '';
  selectedJobType: string = '';
  selectedSalaryRange: string = '';

  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalPages: number = 0;

  // Categories for filter dropdown
  categories: string[] = [
    'IT',
    'Healthcare',
    'Finance',
    'Marketing',
    'Education',
    'Engineering',
    'Sales',
    'Human Resources'
  ];

  // Job types for filter
  jobTypes: string[] = [
    'Full-time',
    'Part-time',
    'Contract',
    'Remote',
    'Internship'
  ];

  // Salary ranges for filter
  salaryRanges: string[] = [
    'Under $30k',
    '$30k - $50k',
    '$50k - $70k',
    '$70k - $100k',
    '$100k - $150k',
    'Above $150k'
  ];

  constructor(private coreDataService: CoreDataService) { }

  ngOnInit(): void {
    this.loadAllJobs();
  }

  loadAllJobs(): void {
    this.loading = true;
    this.error = '';

    this.coreDataService.getAllJobs().subscribe({
      next: (response) => {
        console.log(response, "Jobs");
        this.jobs = response || [];
        this.filteredJobs = [...this.jobs];
        this.calculatePagination();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading jobs:', error);
        this.error = 'Failed to load jobs. Please try again later.';
        this.loading = false;
        // For demo purposes, load sample data if API fails
        // this.loadSampleJobs();
      }
    });
  }

  // loadSampleJobs(): void {
  //   // Sample data for demonstration
  //   this.jobs = [
  //     {
  //       id: 1,
  //       title: 'Senior Software Developer',
  //       company: 'TechCorp Solutions',
  //       location: 'New York, NY',
  //       jobType: 'Full-time',
  //       salary: '$80,000 - $120,000',
  //       category: 'Technology',
  //       description: 'We are looking for an experienced software developer to join our growing team...',
  //       requirements: ['5+ years experience', 'JavaScript', 'Angular', 'Node.js'],
  //       postedDate: '2024-01-15',
  //       isRemote: true
  //     },
  //     {
  //       id: 2,
  //       title: 'Registered Nurse',
  //       company: 'City General Hospital',
  //       location: 'Los Angeles, CA',
  //       jobType: 'Full-time',
  //       salary: '$65,000 - $85,000',
  //       category: 'Healthcare',
  //       description: 'Join our dedicated healthcare team in providing excellent patient care...',
  //       requirements: ['RN License', '2+ years experience', 'BLS Certification'],
  //       postedDate: '2024-01-14',
  //       isRemote: false
  //     },
  //     {
  //       id: 3,
  //       title: 'Marketing Manager',
  //       company: 'Creative Agency Inc',
  //       location: 'Chicago, IL',
  //       jobType: 'Full-time',
  //       salary: '$70,000 - $95,000',
  //       category: 'Marketing',
  //       description: 'Lead our marketing initiatives and drive brand growth...',
  //       requirements: ['Marketing degree', '3+ years experience', 'Digital marketing'],
  //       postedDate: '2024-01-13',
  //       isRemote: false
  //     }
  //   ];
  //   this.filteredJobs = [...this.jobs];
  //   this.calculatePagination();
  // }

  searchJobs(): void {
    if (!this.searchKeyword && !this.selectedLocation && !this.selectedCategory) {
      this.filteredJobs = [...this.jobs];
      this.calculatePagination();
      return;
    }

    // If we have filter criteria, use the filter API
    const filterData = {
      keyword: this.searchKeyword,
      location: this.selectedLocation,
      category: this.selectedCategory,
      jobType: this.selectedJobType,
      salaryRange: this.selectedSalaryRange
    };

    this.loading = true;
    this.coreDataService.GetJobfilterData(filterData).subscribe({
      next: (response) => {
        this.filteredJobs = response || [];
        this.calculatePagination();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error filtering jobs:', error);
        // Fallback to client-side filtering
        this.clientSideFilter();
        this.loading = false;
      }
    });
  }

  clientSideFilter(): void {
    this.filteredJobs = this.jobs.filter(job => {
      const matchesKeyword = !this.searchKeyword ||
        job.title?.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        job.company?.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        job.description?.toLowerCase().includes(this.searchKeyword.toLowerCase());

      const matchesLocation = !this.selectedLocation ||
        job.location?.toLowerCase().includes(this.selectedLocation.toLowerCase());

      const matchesCategory = !this.selectedCategory ||
        job.category === this.selectedCategory;

      const matchesJobType = !this.selectedJobType ||
        job.jobType === this.selectedJobType;

      return matchesKeyword && matchesLocation && matchesCategory && matchesJobType;
    });

    this.calculatePagination();
  }

  clearFilters(): void {
    this.searchKeyword = '';
    this.selectedLocation = '';
    this.selectedCategory = '';
    this.selectedJobType = '';
    this.selectedSalaryRange = '';
    this.filteredJobs = [...this.jobs];
    this.currentPage = 1;
    this.calculatePagination();
  }

  calculatePagination(): void {
    this.totalPages = Math.ceil(this.filteredJobs.length / this.itemsPerPage);
    if (this.currentPage > this.totalPages) {
      this.currentPage = 1;
    }
  }

  get paginatedJobs(): any[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredJobs.slice(startIndex, endIndex);
  }

  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  }
}
