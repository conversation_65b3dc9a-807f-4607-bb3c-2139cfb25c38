/* Page Header */
.page-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Search Filter Section */
.search-filter-section {
  background-color: #f8f9fa;
}

.search-filter-section .card {
  border: none;
  border-radius: 15px;
}

/* Job Cards */
.job-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 15px;
  border: 1px solid #e9ecef;
}

.job-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
  border-color: #007bff;
}

.company-logo {
  min-width: 60px;
  min-height: 60px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.job-title {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.job-title:hover {
  color: #007bff;
  cursor: pointer;
}

.company-name {
  font-weight: 500;
  color: #007bff !important;
  text-decoration: none;
}

.company-name:hover {
  text-decoration: underline;
}

.job-meta .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.job-description {
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.requirements .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Pagination */
.pagination .page-link {
  border-radius: 8px;
  margin: 0 2px;
  border: 1px solid #dee2e6;
  color: #007bff;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

.pagination .page-link:hover {
  background-color: #e9ecef;
  border-color: #007bff;
}

/* Call to Action Section */
.cta-section {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Loading and Error States */
.spinner-border {
  width: 1.5rem;
  height: 1.5rem;
}

.alert {
  border-radius: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .job-card .row {
    flex-direction: column;
  }

  .job-card .col-lg-4 {
    margin-top: 1rem;
    text-align: center !important;
  }

  .job-meta {
    justify-content: center;
  }

  .search-filter-section .row > div {
    margin-bottom: 1rem;
  }

  .pagination {
    flex-wrap: wrap;
  }

  .pagination .page-item {
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 576px) {
  .page-header h1 {
    font-size: 2rem;
  }

  .job-card .card-body {
    padding: 1.5rem;
  }

  .company-logo {
    width: 50px;
    height: 50px;
    min-width: 50px;
  }

  .job-title {
    font-size: 1.1rem;
  }
}

/* Badge Styles */
.badge.bg-outline-secondary {
  background-color: transparent !important;
  border: 1px solid #6c757d;
  color: #6c757d;
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.job-card {
  animation: fadeInUp 0.6s ease-out;
}

.job-card:nth-child(even) {
  animation-delay: 0.1s;
}

.job-card:nth-child(odd) {
  animation-delay: 0.2s;
}

/* Filter Section Enhancements */
.search-filter-section .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.search-filter-section .form-control,
.search-filter-section .form-select {
  border-radius: 8px;
  border: 1px solid #ced4da;
  padding: 0.75rem;
}

.search-filter-section .form-control:focus,
.search-filter-section .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Results Header */
.results-section h3 {
  color: #2c3e50;
  font-weight: 600;
}

/* Dropdown Styling */
.dropdown-toggle {
  border-radius: 8px;
}

.dropdown-menu {
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
